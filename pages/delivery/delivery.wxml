<!-- pages/delivery/delivery.wxml -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>

  <!-- 主要内容 -->
  <block wx:else>
    <!-- 配送信息区域 -->
    <view class="delivery-info">
      <!-- 配送地址 -->
      <view class="info-item" bindtap="showAddressSelector">
        <view class="info-label">配送地址</view>
        <view class="info-content">
          <text class="info-text" wx:if="{{deliveryAddress}}">{{deliveryAddress.detail}}</text>
          <text class="info-placeholder" wx:else>请选择配送地址</text>
          <text class="info-arrow">></text>
        </view>
      </view>

      <!-- 配送时间 -->
      <view class="info-item" bindtap="showTimeSelector">
        <view class="info-label">配送时间</view>
        <view class="info-content">
          <text class="info-text" wx:if="{{selectedTimeSlot}}">{{selectedTimeSlot.label}}</text>
          <text class="info-placeholder" wx:else>请选择配送时间</text>
          <text class="info-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 左右分栏布局 -->
    <view class="main-layout">
      <!-- 左侧分类栏 -->
      <view class="left-sidebar">
        <scroll-view scroll-y="true" class="categories-scroll">
          <view class="category-item {{currentDeliveryType === item.id ? 'active' : ''}}" 
                wx:for="{{deliveryTypes}}" 
                wx:key="id" 
                bindtap="selectDeliveryType" 
                data-id="{{item.id}}">
            <text class="category-text">{{item.name}}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧服务列表 -->
      <view class="right-services">
        <scroll-view scroll-y="true" class="service-list" bindscrolltolower="onReachBottom">
          <view class="service-category-title" wx:if="{{currentServices.length > 0}}">
            配送服务
          </view>

          <view class="service-item" wx:for="{{currentServices}}" wx:key="id">
            <image class="service-image" 
                   src="{{item.image || 'https://vegan.yiheship.com/static/images/default-delivery.png'}}" 
                   mode="aspectFill" 
                   bindtap="showServiceDetail" 
                   data-service="{{item}}">
            </image>
            <view class="service-info">
              <text class="service-name">{{item.name}}</text>
              <text class="service-desc">{{item.description}}</text>
              <view class="service-price-action">
                <text class="service-price">¥{{item.price}}</text>
                <view class="service-action">
                  <view class="action-minus" wx:if="{{item.count > 0}}" bindtap="minusService" data-id="{{item.id}}">-</view>
                  <view class="action-count" wx:if="{{item.count > 0}}">{{item.count}}</view>
                  <view class="action-plus" bindtap="addService" data-id="{{item.id}}">+</view>
                </view>
              </view>
            </view>
          </view>

          <view class="empty-services" wx:if="{{currentServices.length === 0}}">
            <text class="empty-text">该分类暂无配送服务</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 购物车 -->
    <view class="cart-container {{cartVisible ? 'cart-expanded' : ''}}">
      <!-- 添加遮罩层 -->
      <view class="cart-mask" wx:if="{{cartVisible}}" bindtap="toggleCart"></view>

      <view class="cart-header" bindtap="toggleCart">
        <view class="cart-left">
          <view class="cart-icon-container">
            <view class="cart-icon {{cartTotal.count > 0 ? 'active' : ''}}">
              <text class="cart-icon-text">🚚</text>
              <view class="cart-count" wx:if="{{cartTotal.count > 0}}">{{cartTotal.count}}</view>
            </view>
          </view>
          <view class="cart-info">
            <text class="cart-price" wx:if="{{cartTotal.count > 0}}">¥{{cartTotal.price}}</text>
            <text class="cart-empty" wx:else>购物车空空如也</text>
          </view>
        </view>
        <view class="cart-submit {{cartTotal.count > 0 ? 'active' : ''}}" bindtap="{{cartTotal.count > 0 ? 'goToCheckout' : ''}}">
          {{cartTotal.count > 0 ? '去结算' : '请选择服务'}}
        </view>
      </view>

      <!-- 购物车展开内容 -->
      <view class="cart-content" wx:if="{{cartVisible}}">
        <view class="cart-title">
          <text>已选服务</text>
          <text class="cart-clear" bindtap="clearCart">清空购物车</text>
        </view>
        <scroll-view scroll-y="true" class="cart-items">
          <view class="cart-item" wx:for="{{selectedServices}}" wx:key="id">
            <text class="cart-item-name">{{item.name}}</text>
            <view class="cart-item-price-action">
              <text class="cart-item-price">¥{{item.price}}</text>
              <view class="service-action">
                <view class="action-minus" bindtap="minusService" data-id="{{item.id}}">-</view>
                <view class="action-count">{{item.count}}</view>
                <view class="action-plus" bindtap="addService" data-id="{{item.id}}">+</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </block>

  <!-- 服务详情弹窗 -->
  <view class="service-detail-modal" wx:if="{{showServiceModal}}">
    <view class="modal-mask" bindtap="hideServiceDetail"></view>
    <view class="modal-content">
      <view class="modal-close" bindtap="hideServiceDetail">×</view>
      <image class="modal-image" src="{{selectedService.image || 'https://vegan.yiheship.com/static/images/default-delivery.png'}}" mode="aspectFill"></image>
      <view class="modal-info">
        <view class="modal-name">{{selectedService.name}}</view>
        <text class="modal-desc">{{selectedService.description}}</text>
        <view class="modal-price-action">
          <text class="modal-price">¥{{selectedService.price}}</text>
          <view class="service-action">
            <view class="action-minus" wx:if="{{selectedService.count > 0}}" bindtap="minusService" data-id="{{selectedService.id}}">-</view>
            <view class="action-count" wx:if="{{selectedService.count > 0}}">{{selectedService.count}}</view>
            <view class="action-plus" bindtap="addService" data-id="{{selectedService.id}}">+</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 地址选择弹窗 -->
  <view class="address-modal" wx:if="{{showAddressModal}}">
    <view class="modal-mask" bindtap="hideAddressSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>选择配送地址</text>
        <view class="modal-close" bindtap="hideAddressSelector">×</view>
      </view>
      <view class="address-content">
        <text>地址选择功能待实现</text>
      </view>
    </view>
  </view>

  <!-- 时间选择弹窗 -->
  <view class="time-modal" wx:if="{{showTimeModal}}">
    <view class="modal-mask" bindtap="hideTimeSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>选择配送时间</text>
        <view class="modal-close" bindtap="hideTimeSelector">×</view>
      </view>
      <view class="time-slots">
        <view class="time-slot {{selectedTimeSlot && selectedTimeSlot.id === slot.id ? 'selected' : ''}}" 
              wx:for="{{timeSlots}}" 
              wx:key="id" 
              wx:for-item="slot"
              bindtap="selectTimeSlot" 
              data-slot="{{slot}}">
          <text>{{slot.label}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 结算页面 -->
  <view class="checkout-modal" wx:if="{{showCheckout}}">
    <view class="modal-mask" bindtap="hideCheckout"></view>
    <view class="checkout-content">
      <view class="checkout-header">
        <text class="checkout-title">订单确认</text>
        <view class="modal-close" bindtap="hideCheckout">×</view>
      </view>

      <scroll-view scroll-y="true" class="checkout-scroll">
        <!-- 配送信息 -->
        <view class="checkout-section">
          <text class="section-label">配送信息:</text>
          <view class="delivery-summary">
            <view class="summary-item">
              <text class="summary-label">配送地址:</text>
              <text class="summary-value">{{deliveryAddress.detail || '未选择'}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">配送时间:</text>
              <text class="summary-value">{{selectedTimeSlot.label || '未选择'}}</text>
            </view>
          </view>
        </view>

        <!-- 服务列表 -->
        <view class="checkout-section">
          <text class="section-label">已选服务:</text>
          <scroll-view scroll-y="true" class="checkout-items">
            <block wx:if="{{selectedServices && selectedServices.length > 0}}">
              <view class="checkout-item" wx:for="{{selectedServices}}" wx:key="id">
                <text class="checkout-item-name">{{item.name}}</text>
                <text class="checkout-item-count">x{{item.count}}</text>
                <text class="checkout-item-price">¥{{item.price * item.count}}</text>
              </view>
            </block>
            <view class="checkout-empty" wx:else>
              <text>购物车中暂无服务</text>
            </view>
          </scroll-view>
        </view>

        <view class="checkout-total">
          <text>合计: ¥{{finalAmount || cartTotal.price}}</text>
        </view>
      </scroll-view>

      <view class="checkout-submit" bindtap="submitOrder">提交订单</view>
    </view>
  </view>

  <!-- 订单成功提示 -->
  <view class="order-success" wx:if="{{orderSuccess}}">
    <view class="success-icon">✓</view>
    <text class="success-text">配送订单提交成功！</text>
    <text class="success-order-id">订单号: {{orderId}}</text>
    <view class="success-actions">
      <view class="success-btn primary" bindtap="goToOrderDetail">查看订单</view>
    </view>
  </view>
</view>
