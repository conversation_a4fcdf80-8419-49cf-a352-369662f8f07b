// pages/delivery/delivery.js
import { loginRequest } from '../../service/index';

Page({
  data: {
    loading: true,
    deliveryTypes: [],
    currentDeliveryType: '0',
    deliveryServices: [],
    currentServices: [],
    selectedServices: [],
    cartTotal: {
      count: 0,
      price: 0
    },
    cartVisible: false,
    showServiceModal: false,
    selectedService: {},
    pageNo: 1,
    pageSize: 20,
    hasMore: true,
    showCouponList: false,
    selectedCoupons: [],
    selectedCouponIds: [],
    products: [],
    showCheckout: false,
    couponDiscount: 0,
    finalAmount: 0,
    orderSuccess: false,
    orderId: '',
    // 支付相关数据
    showPaymentOptions: false,
    selectedPaymentMethod: 'wxpay',
    canUseBalance: false,
    userBalance: 0,
    payableAmount: 0,
    paymentOrderData: null,
    enterpriseList: [],
    selectedEnterprise: null,
    source: 'delivery',
    // 配送相关数据
    deliveryAddress: null,
    selectedTimeSlot: null,
    timeSlots: [],
    deliveryFee: 0,
    showAddressModal: false,
    showTimeModal: false
  },

  onLoad: function (options) {
    wx.setNavigationBarTitle({
      title: '配送服务'
    });
    
    this.fetchDeliveryTypes();
    this.loadUserAddress();
    this.generateTimeSlots();
  },

  onPullDownRefresh: function () {
    this.setData({
      pageNo: 1,
      hasMore: true
    });
    this.fetchDeliveryServices(this.data.currentDeliveryType);
    wx.stopPullDownRefresh();
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        pageNo: this.data.pageNo + 1
      });
      this.fetchDeliveryServices(this.data.currentDeliveryType, true);
    }
  },

  /**
   * 获取配送类型
   */
  fetchDeliveryTypes() {
    this.setData({ loading: true });

    loginRequest.get({
      url: '/delivery/types',
      data: {
        show_root: false
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200 && res.data && res.data.length > 0) {
        const deliveryTypes = res.data || [];
        this.setData({
          deliveryTypes,
          currentDeliveryType: deliveryTypes[0]?.id || '0',
          loading: false
        });
        
        this.fetchDeliveryServices(deliveryTypes[0]?.id || '0');
      } else {
        this.setData({
          deliveryTypes: [],
          loading: false
        });
        this.fetchDeliveryServices('0');
      }
    }).catch(err => {
      console.error('获取配送类型失败', err);
      this.setData({
        deliveryTypes: [],
        loading: false
      });
      this.fetchDeliveryServices('0');
    });
  },

  /**
   * 获取配送服务列表
   */
  fetchDeliveryServices(typeId, append = false) {
    const { pageNo, pageSize } = this.data;

    this.setData({
      loading: !append
    });

    const app = getApp();
    const storeId = app.globalData.defaultStoreId || 1;

    loginRequest.get({
      url: '/delivery/services',
      data: {
        store_id: storeId,
        type_id: typeId,
        page_no: pageNo,
        page_size: pageSize
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        const newServices = res.data.list || [];
        const hasMore = newServices.length === pageSize;

        newServices.forEach(service => {
          const selectedItem = this.data.selectedServices.find(item => item.id === service.id);
          service.count = selectedItem ? selectedItem.count : 0;
        });

        if (append) {
          const updatedServices = [...this.data.currentServices, ...newServices];
          this.setData({
            currentServices: updatedServices,
            hasMore,
            loading: false
          });
        } else {
          this.setData({
            deliveryServices: newServices,
            currentServices: newServices,
            pageNo: 2,
            hasMore,
            loading: false
          });
        }
      } else {
        this.setData({
          loading: false
        });
        if (!append) {
          this.setData({
            currentServices: []
          });
        }

        if (res.message) {
          wx.showToast({
            title: res.message,
            icon: 'none'
          });
        }
      }
    }).catch(err => {
      console.error('获取配送服务失败', err);
      this.setData({
        loading: false
      });
      if (!append) {
        this.setData({
          currentServices: []
        });
      }

      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 选择配送类型
   */
  selectDeliveryType(e) {
    const typeId = e.currentTarget.dataset.id;
    this.setData({
      currentDeliveryType: typeId,
      pageNo: 1,
      hasMore: true
    });
    this.fetchDeliveryServices(typeId);
  },

  /**
   * 添加配送服务
   */
  addService(e) {
    const serviceId = e.currentTarget.dataset.id;
    this.updateCart(serviceId, 1);
  },

  /**
   * 减少配送服务
   */
  minusService(e) {
    const serviceId = e.currentTarget.dataset.id;
    this.updateCart(serviceId, -1);
  },

  /**
   * 更新购物车
   */
  updateCart(serviceId, change) {
    const service = this.data.deliveryServices.find(item => item.id === serviceId) || 
                   this.data.currentServices.find(item => item.id === serviceId);
    if (!service) return;

    const oldCount = service.count || 0;
    const newCount = Math.max(0, oldCount + change);
    service.count = newCount;

    const deliveryServices = this.data.deliveryServices.map(item => {
      if (item.id === serviceId) {
        item.count = newCount;
      }
      return item;
    });

    const currentServices = this.data.currentServices.map(item => {
      if (item.id === serviceId) {
        item.count = newCount;
      }
      return item;
    });

    let selectedServices = [...this.data.selectedServices];

    if (newCount > 0) {
      const serviceIndex = selectedServices.findIndex(item => item.id === serviceId);
      if (serviceIndex >= 0) {
        selectedServices[serviceIndex].count = newCount;
      } else {
        selectedServices.push({
          id: service.id,
          name: service.name,
          price: service.price,
          count: newCount
        });
      }
    } else {
      selectedServices = selectedServices.filter(item => item.id !== serviceId);
    }

    const cartTotal = this.calculateCartTotal(selectedServices);

    this.setData({
      deliveryServices,
      currentServices,
      selectedServices,
      cartTotal
    });
  },

  /**
   * 计算购物车总数和总价
   */
  calculateCartTotal(selectedServices) {
    let count = 0;
    let price = 0;

    selectedServices.forEach(item => {
      count += item.count;
      price += item.price * item.count;
    });

    return {
      count,
      price: parseFloat(price.toFixed(2))
    };
  },

  /**
   * 切换购物车展开/收起状态
   */
  toggleCart() {
    if (this.data.cartTotal.count > 0) {
      this.setData({
        cartVisible: !this.data.cartVisible
      });
    }
  },

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: '提示',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          const deliveryServices = this.data.deliveryServices.map(item => ({ ...item, count: 0 }));
          const currentServices = this.data.currentServices.map(item => ({ ...item, count: 0 }));

          this.setData({
            deliveryServices,
            currentServices,
            selectedServices: [],
            cartTotal: { count: 0, price: 0 },
            cartVisible: false
          });
        }
      }
    });
  },

  /**
   * 显示服务详情
   */
  showServiceDetail(e) {
    const service = e.currentTarget.dataset.service;
    this.setData({
      selectedService: service,
      showServiceModal: true
    });
  },

  /**
   * 隐藏服务详情
   */
  hideServiceDetail() {
    this.setData({
      showServiceModal: false,
      selectedService: {}
    });
  },

  /**
   * 加载用户地址
   */
  loadUserAddress() {
    // 从本地存储或API获取用户默认地址
    const address = wx.getStorageSync('defaultAddress');
    if (address) {
      this.setData({
        deliveryAddress: address
      });
    }
  },

  /**
   * 生成时间段
   */
  generateTimeSlots() {
    const timeSlots = [
      { id: '1', label: '09:00-12:00', value: '09:00-12:00' },
      { id: '2', label: '12:00-15:00', value: '12:00-15:00' },
      { id: '3', label: '15:00-18:00', value: '15:00-18:00' },
      { id: '4', label: '18:00-21:00', value: '18:00-21:00' }
    ];
    this.setData({
      timeSlots
    });
  },

  /**
   * 显示地址选择
   */
  showAddressSelector() {
    this.setData({
      showAddressModal: true
    });
  },

  /**
   * 隐藏地址选择
   */
  hideAddressSelector() {
    this.setData({
      showAddressModal: false
    });
  },

  /**
   * 显示时间选择
   */
  showTimeSelector() {
    this.setData({
      showTimeModal: true
    });
  },

  /**
   * 隐藏时间选择
   */
  hideTimeSelector() {
    this.setData({
      showTimeModal: false
    });
  },

  /**
   * 选择时间段
   */
  selectTimeSlot(e) {
    const timeSlot = e.currentTarget.dataset.slot;
    this.setData({
      selectedTimeSlot: timeSlot,
      showTimeModal: false
    });
  },

  /**
   * 去结算
   */
  goToCheckout() {
    if (this.data.cartTotal.count === 0) {
      wx.showToast({
        title: '请先选择配送服务',
        icon: 'none'
      });
      return;
    }

    if (!this.data.deliveryAddress) {
      wx.showToast({
        title: '请先选择配送地址',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedTimeSlot) {
      wx.showToast({
        title: '请先选择配送时间',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCheckout: true,
      finalAmount: this.data.cartTotal.price + this.data.deliveryFee
    });
  },

  /**
   * 隐藏结算页面
   */
  hideCheckout() {
    this.setData({
      showCheckout: false
    });
  },

  /**
   * 提交订单
   */
  submitOrder() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=delivery'
      });
      return;
    }

    this.createDeliveryOrder();
  },

  /**
   * 创建配送订单
   */
  createDeliveryOrder() {
    wx.showLoading({
      title: '提交中...'
    });

    // 准备订单数据
    const orderItems = this.data.selectedServices.map(item => ({
      service_id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.count
    }));

    const orderData = {
      order_type: 'delivery',
      items: orderItems,
      total_amount: this.data.finalAmount || this.data.cartTotal.price,
      delivery_address: this.data.deliveryAddress,
      delivery_time: this.data.selectedTimeSlot,
      delivery_fee: this.data.deliveryFee
    };

    console.log('提交配送订单数据:', orderData);

    loginRequest.post({
      url: '/delivery-order/create',
      data: orderData,
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      wx.hideLoading();

      console.log('配送订单创建响应:', JSON.stringify(res));

      if (res.order_no) {
        this.setData({
          showCheckout: false,
          orderSuccess: true,
          orderId: res.order_no
        });

        // 清空购物车
        this.clearCartAfterOrder();

        wx.showToast({
          title: '订单提交成功',
          icon: 'success',
          duration: 2000
        });
      } else if (res.status === 401) {
        wx.navigateTo({
          url: '/pages/phoneAuth/phoneAuth?redirect=delivery'
        });
      } else {
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('请求失败', err);
      wx.hideLoading();

      wx.showToast({
        title: err.message || '网络错误，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 订单成功后清空购物车
   */
  clearCartAfterOrder() {
    const deliveryServices = this.data.deliveryServices.map(item => ({ ...item, count: 0 }));
    const currentServices = this.data.currentServices.map(item => ({ ...item, count: 0 }));

    this.setData({
      deliveryServices,
      currentServices,
      selectedServices: [],
      cartTotal: { count: 0, price: 0 },
      cartVisible: false
    });
  },

  /**
   * 跳转到订单详情
   */
  goToOrderDetail() {
    wx.switchTab({
      url: '/pages/reserve/reserve?tab=all'
    });
  }
});
